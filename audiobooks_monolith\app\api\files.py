"""
文件管理API路由
"""

from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from typing import Optional, List
import os
import uuid

from app.core.database import get_db
from app.core.auth import get_current_active_user, require_points
from app.core.config import settings
from app.core.logging import get_logger
from app.services.storage_service import storage_service
from app.models.user import User
from app.models.task import Task, TaskStatus, TaskType
from app.services.task_processing_service import task_processing_service

logger = get_logger(__name__)
router = APIRouter()


async def _upload_file_logic(
    file: UploadFile,
    task_id: Optional[str],
    current_user: User,
    db: Session,
    architecture: str = "本地存储"
):
    """通用文件上传逻辑"""
    # 检查文件大小
    if file.size and file.size > settings.max_file_size:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"文件大小超过限制 ({settings.max_file_size / 1024 / 1024:.1f}MB)"
        )

    # 检查文件类型
    file_ext = os.path.splitext(file.filename)[1].lower()
    if file_ext not in settings.allowed_file_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"不支持的文件类型: {file_ext}"
        )

    # 生成任务ID（如果未提供）
    if not task_id:
        task_id = str(uuid.uuid4())

    # 生成存储路径
    storage_path = storage_service.generate_original_file_storage_path(
        str(current_user.id), task_id, file.filename
    )

    # 保存临时文件
    temp_path = os.path.join(settings.temp_dir, f"upload_{uuid.uuid4()}_{file.filename}")
    os.makedirs(os.path.dirname(temp_path), exist_ok=True)

    with open(temp_path, "wb") as temp_file:
        content = await file.read()
        temp_file.write(content)

    # 上传到存储
    file_url = await storage_service.upload_file(
        temp_path, storage_path, file.content_type
    )

    # 清理临时文件
    try:
        os.unlink(temp_path)
    except:
        pass

    if not file_url:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="文件上传失败"
        )

    # 根据文件扩展名确定任务类型
    task_type_mapping = {
        '.epub': TaskType.EPUB,
        '.txt': TaskType.TXT,
        '.pdf': TaskType.PDF,
        '.docx': TaskType.DOCX
    }
    task_type = task_type_mapping.get(file_ext, TaskType.TXT)

    # 创建任务记录
    db_task = Task(
        user_id=current_user.id,
        title=os.path.splitext(file.filename)[0],  # 使用文件名作为标题
        task_type=task_type,
        status=TaskStatus.PENDING,
        original_filename=file.filename,
        file_size=len(content),
        file_url=file_url,
        processing_mode="standard",  # 默认标准模式
        progress=0
    )

    db.add(db_task)
    db.commit()
    db.refresh(db_task)

    logger.info(f"文件上传成功并创建任务: {db_task.id}, 用户: {current_user.id}")

    # 启动异步任务处理
    try:
        await task_processing_service.start_task_processing(db_task.id)
        logger.info(f"任务处理已启动: {db_task.id}")
    except Exception as e:
        logger.error(f"启动任务处理失败: {str(e)}")
        # 不影响文件上传的成功响应

    return {
        "message": "文件上传成功并开始转换",
        "task_id": str(db_task.id),
        "taskId": str(db_task.id),  # 前端期望的字段名
        "filename": file.filename,
        "file_url": file_url,
        "file_size": len(content),
        "storage_path": storage_path,
        "architecture": architecture,
        "status": db_task.status.value,
        "title": db_task.title
    }


@router.post("/upload")
async def upload_file(
    file: UploadFile = File(...),
    task_id: Optional[str] = Form(None),
    email: Optional[str] = Form(None),  # 前端发送的额外参数，忽略
    current_user: User = Depends(require_points(settings.points_per_conversion)),
    db: Session = Depends(get_db)
):
    """上传文件 - 原始端点"""
    try:
        return await _upload_file_logic(file, task_id, current_user, db, "本地存储")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件上传失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="文件上传失败"
        )


@router.post("/upload/file-r2")
async def upload_file_r2(
    file: UploadFile = File(...),
    task_id: Optional[str] = Form(None),
    email: Optional[str] = Form(None),  # 前端发送的额外参数，忽略
    current_user: User = Depends(require_points(settings.points_per_conversion)),
    db: Session = Depends(get_db)
):
    """上传文件 - R2架构端点（当前使用本地存储）"""
    try:
        return await _upload_file_logic(file, task_id, current_user, db, "R2")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件上传失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="文件上传失败"
        )


@router.post("/upload/file")
async def upload_file_traditional(
    file: UploadFile = File(...),
    task_id: Optional[str] = Form(None),
    email: Optional[str] = Form(None),  # 前端发送的额外参数，忽略
    current_user: User = Depends(require_points(settings.points_per_conversion)),
    db: Session = Depends(get_db)
):
    """上传文件 - 传统架构端点"""
    try:
        return await _upload_file_logic(file, task_id, current_user, db, "传统")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件上传失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="文件上传失败"
        )


@router.get("/download/{storage_path:path}")
async def download_file(
    storage_path: str,
    current_user: User = Depends(get_current_active_user)
):
    """下载文件"""
    try:
        # 检查文件是否属于当前用户
        user_id, task_id = storage_service.parse_file_url_info(storage_path)
        if user_id and str(current_user.id) != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此文件"
            )
        
        # 获取文件信息
        file_info = await storage_service.get_file_info(storage_path)
        if not file_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文件不存在"
            )
        
        # 获取本地文件路径
        local_path = storage_service.get_local_file_path(storage_path)
        if not local_path.exists():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文件不存在"
            )
        
        # 返回文件
        return FileResponse(
            path=str(local_path),
            filename=file_info.get("original_filename", local_path.name),
            media_type=file_info.get("content_type", "application/octet-stream")
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件下载失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="文件下载失败"
        )


@router.delete("/delete/{storage_path:path}")
async def delete_file(
    storage_path: str,
    current_user: User = Depends(get_current_active_user)
):
    """删除文件"""
    try:
        # 检查文件是否属于当前用户
        user_id, task_id = storage_service.parse_file_url_info(storage_path)
        if user_id and str(current_user.id) != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权删除此文件"
            )
        
        # 删除文件
        success = await storage_service.delete_file(storage_path)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文件不存在或删除失败"
            )
        
        return {"message": "文件删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件删除失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="文件删除失败"
        )


@router.get("/list")
async def list_user_files(
    current_user: User = Depends(get_current_active_user)
):
    """列出用户文件"""
    try:
        # 列出用户目录下的所有文件
        user_prefix = f"users/{current_user.id}"
        files = await storage_service.list_files(user_prefix)
        
        return {
            "files": files,
            "total_count": len(files)
        }
        
    except Exception as e:
        logger.error(f"列出用户文件失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取文件列表失败"
        )


@router.get("/info/{storage_path:path}")
async def get_file_info(
    storage_path: str,
    current_user: User = Depends(get_current_active_user)
):
    """获取文件信息"""
    try:
        # 检查文件是否属于当前用户
        user_id, task_id = storage_service.parse_file_url_info(storage_path)
        if user_id and str(current_user.id) != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此文件"
            )
        
        # 获取文件信息
        file_info = await storage_service.get_file_info(storage_path)
        if not file_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文件不存在"
            )
        
        return file_info
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文件信息失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取文件信息失败"
        )


@router.get("/storage/stats")
async def get_storage_stats(
    current_user: User = Depends(get_current_active_user)
):
    """获取存储统计信息"""
    try:
        # 获取全局存储统计
        global_stats = storage_service.get_storage_stats()
        
        # 获取用户文件统计
        user_prefix = f"users/{current_user.id}"
        user_files = await storage_service.list_files(user_prefix)
        
        user_total_size = sum(file.get("size", 0) for file in user_files)
        
        return {
            "global_stats": global_stats,
            "user_stats": {
                "total_files": len(user_files),
                "total_size": user_total_size,
                "total_size_mb": round(user_total_size / (1024 * 1024), 2)
            }
        }
        
    except Exception as e:
        logger.error(f"获取存储统计失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取存储统计失败"
        )
