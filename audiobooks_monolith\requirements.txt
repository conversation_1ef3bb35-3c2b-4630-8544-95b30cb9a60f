# FastAPI core dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP clients
httpx==0.25.2
requests==2.31.0
aiohttp==3.9.1

# Database
sqlalchemy==2.0.23
alembic==1.13.1

# Authentication and security
bcrypt==4.1.2
python-jose[cryptography]==3.3.0
python-multipart==0.0.6

# EPUB file processing
ebooklib==0.18
beautifulsoup4==4.12.2
lxml==4.9.3

# Audio processing
pydub==0.25.1

# Async file handling
aiofiles==23.2.1

# Template engine
jinja2==3.1.2

# Logging and monitoring
structlog==23.2.0

# Development and testing tools (optional)
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0

# Other tools
python-dotenv==1.0.0
Pillow==10.1.0
chardet==5.2.0

# File type detection
python-magic==0.4.27

# Date/time handling
python-dateutil==2.8.2
