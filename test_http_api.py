#!/usr/bin/env python3
"""
测试HTTP API调用
"""

import requests
import json

def test_audio_library_http():
    """测试音频库HTTP API"""
    print("=== 测试音频库HTTP API ===")
    
    try:
        # 首先需要登录获取token
        login_data = {
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        # 尝试登录
        login_response = requests.post(
            "http://localhost:8001/api/auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            token = login_result.get("access_token")
            print(f"✅ 登录成功，获取到token")
            
            # 使用token调用音频库API
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            api_response = requests.get(
                "http://localhost:8001/api/audio-library",
                headers=headers
            )
            
            print(f"API响应状态码: {api_response.status_code}")
            
            if api_response.status_code == 200:
                result = api_response.json()
                print(f"✅ 音频库API调用成功！")
                print(f"返回的音频书籍数量: {result.get('total', 0)}")
                print(f"统计信息: {result.get('statistics', {})}")
                
                audio_books = result.get('audioBooks', [])
                for i, book in enumerate(audio_books):
                    print(f"\n音频书籍 {i+1}:")
                    print(f"  ID: {book.get('id')}")
                    print(f"  标题: {book.get('title')}")
                    print(f"  文件名: {book.get('filename')}")
                    print(f"  时长: {book.get('duration')}")
                    print(f"  大小: {book.get('size')}")
                
                return True
            else:
                print(f"❌ 音频库API调用失败: {api_response.status_code}")
                try:
                    error_detail = api_response.json()
                    print(f"错误详情: {error_detail}")
                except:
                    print(f"错误内容: {api_response.text}")
                return False
                
        else:
            print(f"❌ 登录失败: {login_response.status_code}")
            try:
                error_detail = login_response.json()
                print(f"登录错误详情: {error_detail}")
            except:
                print(f"登录错误内容: {login_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ HTTP测试失败: {str(e)}")
        return False

def test_tasks_api():
    """测试任务列表API"""
    print("\n=== 测试任务列表API ===")
    
    try:
        # 首先需要登录获取token
        login_data = {
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        # 尝试登录
        login_response = requests.post(
            "http://localhost:8001/api/auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            token = login_result.get("access_token")
            
            # 使用token调用任务列表API
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            api_response = requests.get(
                "http://localhost:8001/api/tasks",
                headers=headers
            )
            
            print(f"任务API响应状态码: {api_response.status_code}")
            
            if api_response.status_code == 200:
                tasks = api_response.json()
                print(f"✅ 任务列表API调用成功！")
                print(f"返回的任务数量: {len(tasks)}")
                
                for i, task in enumerate(tasks):
                    print(f"\n任务 {i+1}:")
                    print(f"  ID: {task.get('id')}")
                    print(f"  标题: {task.get('title')}")
                    print(f"  状态: {task.get('status')}")
                    print(f"  任务类型: {task.get('task_type')}")
                    print(f"  文件大小: {task.get('file_size')}")
                    print(f"  总时长: {task.get('total_duration')}")
                
                return True
            else:
                print(f"❌ 任务列表API调用失败: {api_response.status_code}")
                try:
                    error_detail = api_response.json()
                    print(f"错误详情: {error_detail}")
                except:
                    print(f"错误内容: {api_response.text}")
                return False
                
        else:
            print(f"❌ 登录失败: {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 任务API测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("开始HTTP API测试...\n")
    
    audio_success = test_audio_library_http()
    tasks_success = test_tasks_api()
    
    print("\n=== 测试总结 ===")
    if audio_success and tasks_success:
        print("✅ 所有HTTP API测试成功！")
    else:
        print("❌ 部分HTTP API测试失败！")
