{% extends "base.html" %}

{% block title %}创作工坊 - 有声图书生成器{% endblock %}

{% block content %}
<div class="workshop-container">
    <div class="workshop-header">
        <h1><i class="fas fa-tools"></i> 创作工坊</h1>
        <p>上传您的文档，开始创作有声图书</p>
    </div>

    <div class="upload-section">
        <div class="upload-card">
            <div class="upload-area" id="uploadArea">
                <div class="upload-icon">
                    <i class="fas fa-cloud-upload-alt"></i>
                </div>
                <h3>拖拽文件到此处或点击上传</h3>
                <p>支持 EPUB、PDF、TXT、Word 格式</p>
                <p class="file-size-limit">文件大小限制：50MB</p>
                <input type="file" id="fileInput" accept=".epub,.pdf,.txt,.docx,.doc" style="display: none;">
                <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                    <i class="fas fa-plus"></i> 选择文件
                </button>
            </div>
            
            <div class="upload-progress" id="uploadProgress" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">上传中...</div>
            </div>
        </div>
    </div>

    <div class="tasks-section">
        <div class="section-header">
            <h2><i class="fas fa-tasks"></i> 我的任务</h2>
            <button class="refresh-btn" onclick="app.loadTasks()">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
        </div>
        
        <div class="tasks-container" id="tasksContainer">
            <div class="loading-state">
                <i class="fas fa-spinner fa-spin"></i>
                <p>加载任务列表...</p>
            </div>
        </div>
    </div>
</div>

<style>
.workshop-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.workshop-header {
    text-align: center;
    margin-bottom: 3rem;
}

.workshop-header h1 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.workshop-header p {
    color: #7f8c8d;
    font-size: 1.1rem;
}

.upload-section {
    margin-bottom: 3rem;
}

.upload-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.upload-area {
    padding: 3rem;
    text-align: center;
    border: 2px dashed #e0e6ed;
    margin: 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #3498db;
    background-color: #f8f9fa;
}

.upload-area.dragover {
    border-color: #3498db;
    background-color: #e3f2fd;
}

.upload-icon {
    font-size: 3rem;
    color: #3498db;
    margin-bottom: 1rem;
}

.upload-area h3 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.upload-area p {
    color: #7f8c8d;
    margin-bottom: 0.5rem;
}

.file-size-limit {
    font-size: 0.9rem;
    color: #95a5a6;
}

.upload-btn {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-size: 1rem;
    cursor: pointer;
    margin-top: 1rem;
    transition: all 0.3s ease;
}

.upload-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.upload-progress {
    padding: 2rem;
    background: #f8f9fa;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e0e6ed;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2980b9);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    text-align: center;
    color: #2c3e50;
    font-weight: 500;
}

.tasks-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e6ed;
}

.section-header h2 {
    color: #2c3e50;
    margin: 0;
}

.refresh-btn {
    background: #95a5a6;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.refresh-btn:hover {
    background: #7f8c8d;
}

.tasks-container {
    min-height: 300px;
    padding: 2rem;
}

.loading-state {
    text-align: center;
    color: #7f8c8d;
}

.loading-state i {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.task-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border-left: 4px solid #3498db;
}

.task-item.processing {
    border-left-color: #f39c12;
}

.task-item.completed {
    border-left-color: #27ae60;
}

.task-item.failed {
    border-left-color: #e74c3c;
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.task-title {
    font-weight: 600;
    color: #2c3e50;
}

.task-status {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.task-status.pending {
    background: #f39c12;
    color: white;
}

.task-status.processing {
    background: #3498db;
    color: white;
}

.task-status.completed {
    background: #27ae60;
    color: white;
}

.task-status.failed {
    background: #e74c3c;
    color: white;
}

.task-progress {
    margin: 1rem 0;
}

.task-progress-bar {
    width: 100%;
    height: 6px;
    background: #e0e6ed;
    border-radius: 3px;
    overflow: hidden;
}

.task-progress-fill {
    height: 100%;
    background: #3498db;
    transition: width 0.3s ease;
}

.task-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.task-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.task-btn.primary {
    background: #3498db;
    color: white;
}

.task-btn.danger {
    background: #e74c3c;
    color: white;
}

.task-btn:hover {
    opacity: 0.8;
}

@media (max-width: 768px) {
    .workshop-container {
        padding: 1rem;
    }
    
    .upload-area {
        padding: 2rem 1rem;
    }
    
    .section-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}
</style>

<script>
// 文件上传相关功能
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const uploadProgress = document.getElementById('uploadProgress');
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');

    // 拖拽上传
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileUpload(files[0]);
        }
    });

    // 点击上传
    fileInput.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            handleFileUpload(e.target.files[0]);
        }
    });

    function handleFileUpload(file) {
        // 检查文件类型
        const allowedTypes = ['.epub', '.pdf', '.txt', '.docx', '.doc'];
        const fileExt = '.' + file.name.split('.').pop().toLowerCase();
        
        if (!allowedTypes.includes(fileExt)) {
            alert('不支持的文件类型。请上传 EPUB、PDF、TXT 或 Word 文档。');
            return;
        }

        // 检查文件大小 (50MB)
        if (file.size > 50 * 1024 * 1024) {
            alert('文件大小超过限制（50MB）。');
            return;
        }

        // 调用app.js中的上传函数
        if (window.app && window.app.uploadFile) {
            window.app.uploadFile(file);
        } else {
            console.error('App not initialized');
        }
    }

    // 初始化加载任务
    if (window.app && window.app.loadTasks) {
        window.app.loadTasks();
    }
});
</script>
{% endblock %}
