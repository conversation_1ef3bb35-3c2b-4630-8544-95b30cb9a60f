#!/usr/bin/env python3
"""
直接测试音频库API
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'audiobooks_monolith'))

import asyncio
from app.core.database import get_db
from app.models.task import Task, TaskStatus
from app.models.user import User
from app.api.audio import get_audio_library
from sqlalchemy.orm import Session

async def test_audio_library_api_direct():
    """直接测试音频库API函数"""
    print("=== 直接测试音频库API ===")
    
    try:
        # 获取数据库会话
        db = next(get_db())
        
        # 创建一个模拟用户对象（用户ID为4）
        class MockUser:
            def __init__(self, user_id):
                self.id = user_id
        
        mock_user = MockUser(4)
        
        # 直接调用API函数
        result = await get_audio_library(
            sort="recent",
            current_user=mock_user,
            db=db
        )
        
        print(f"API调用成功！")
        print(f"返回的音频书籍数量: {result['total']}")
        print(f"统计信息: {result['statistics']}")
        
        for i, book in enumerate(result['audioBooks']):
            print(f"\n音频书籍 {i+1}:")
            print(f"  ID: {book['id']}")
            print(f"  标题: {book['title']}")
            print(f"  文件名: {book['filename']}")
            print(f"  状态: {book['status']}")
            print(f"  时长: {book['duration']}")
            print(f"  大小: {book['size']}")
            print(f"  章节数: {book['totalChapters']}")
            print(f"  音频URL: {book['audioUrl']}")
            print(f"  播放列表: {book['playlistUrl']}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ API测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始直接测试音频库API...\n")
    
    success = asyncio.run(test_audio_library_api_direct())
    
    print("\n=== 测试结果 ===")
    if success:
        print("✅ 音频库API测试成功！")
    else:
        print("❌ 音频库API测试失败！")
