"""
文本提取服务
支持从EPUB、PDF、TXT、DOCX文件中提取文本内容
"""

import os
import re
from pathlib import Path
from typing import Optional, List, Dict, Any
import asyncio
import aiofiles

from app.core.logging import get_logger
from app.core.config import settings

logger = get_logger(__name__)


class TextExtractionService:
    """文本提取服务"""
    
    def __init__(self):
        self.supported_formats = ['.epub', '.pdf', '.txt', '.docx']
    
    async def extract_text_from_file(self, file_path: str, file_type: str) -> Dict[str, Any]:
        """
        从文件中提取文本
        
        Args:
            file_path: 文件路径
            file_type: 文件类型 (epub, pdf, txt, docx)
            
        Returns:
            Dict: 包含提取的文本和元数据
        """
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            logger.info(f"开始提取文本: {file_path}, 类型: {file_type}")
            
            if file_type.lower() == 'txt':
                return await self._extract_from_txt(file_path)
            elif file_type.lower() == 'epub':
                return await self._extract_from_epub(file_path)
            elif file_type.lower() == 'pdf':
                return await self._extract_from_pdf(file_path)
            elif file_type.lower() == 'docx':
                return await self._extract_from_docx(file_path)
            else:
                raise ValueError(f"不支持的文件类型: {file_type}")
                
        except Exception as e:
            logger.error(f"文本提取失败: {str(e)}")
            raise
    
    async def _extract_from_txt(self, file_path: Path) -> Dict[str, Any]:
        """从TXT文件提取文本"""
        try:
            # 尝试多种编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16']
            content = None
            
            for encoding in encodings:
                try:
                    async with aiofiles.open(file_path, 'r', encoding=encoding) as f:
                        content = await f.read()
                    break
                except UnicodeDecodeError:
                    continue
            
            if content is None:
                raise ValueError("无法解码文件，请检查文件编码")
            
            # 清理文本
            content = self._clean_text(content)
            
            return {
                "title": file_path.stem,
                "content": content,
                "chapters": [{"title": "全文", "content": content}],
                "word_count": len(content),
                "metadata": {
                    "file_type": "txt",
                    "encoding": "utf-8"
                }
            }
            
        except Exception as e:
            logger.error(f"TXT文件提取失败: {str(e)}")
            raise
    
    async def _extract_from_epub(self, file_path: Path) -> Dict[str, Any]:
        """从EPUB文件提取文本"""
        try:
            import ebooklib
            from ebooklib import epub
            from bs4 import BeautifulSoup
            
            # 在异步环境中运行同步代码
            def _read_epub():
                book = epub.read_epub(str(file_path))
                
                title = book.get_metadata('DC', 'title')[0][0] if book.get_metadata('DC', 'title') else file_path.stem
                
                chapters = []
                full_content = ""
                
                for item in book.get_items():
                    if item.get_type() == ebooklib.ITEM_DOCUMENT:
                        soup = BeautifulSoup(item.get_content(), 'html.parser')
                        text = soup.get_text()
                        text = self._clean_text(text)
                        
                        if text.strip():
                            chapter_title = f"章节 {len(chapters) + 1}"
                            chapters.append({
                                "title": chapter_title,
                                "content": text
                            })
                            full_content += text + "\n\n"
                
                return title, chapters, full_content
            
            # 在线程池中执行
            title, chapters, full_content = await asyncio.get_event_loop().run_in_executor(
                None, _read_epub
            )
            
            return {
                "title": title,
                "content": full_content,
                "chapters": chapters,
                "word_count": len(full_content),
                "metadata": {
                    "file_type": "epub",
                    "chapter_count": len(chapters)
                }
            }
            
        except ImportError:
            logger.error("缺少ebooklib依赖，无法处理EPUB文件")
            raise ValueError("系统不支持EPUB文件格式")
        except Exception as e:
            logger.error(f"EPUB文件提取失败: {str(e)}")
            raise
    
    async def _extract_from_pdf(self, file_path: Path) -> Dict[str, Any]:
        """从PDF文件提取文本"""
        try:
            import PyPDF2
            
            def _read_pdf():
                content = ""
                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    
                    for page_num, page in enumerate(pdf_reader.pages):
                        try:
                            text = page.extract_text()
                            if text:
                                content += text + "\n"
                        except Exception as e:
                            logger.warning(f"PDF第{page_num + 1}页提取失败: {str(e)}")
                            continue
                
                return content
            
            # 在线程池中执行
            content = await asyncio.get_event_loop().run_in_executor(
                None, _read_pdf
            )
            
            content = self._clean_text(content)
            
            return {
                "title": file_path.stem,
                "content": content,
                "chapters": [{"title": "全文", "content": content}],
                "word_count": len(content),
                "metadata": {
                    "file_type": "pdf"
                }
            }
            
        except ImportError:
            logger.error("缺少PyPDF2依赖，无法处理PDF文件")
            raise ValueError("系统不支持PDF文件格式")
        except Exception as e:
            logger.error(f"PDF文件提取失败: {str(e)}")
            raise
    
    async def _extract_from_docx(self, file_path: Path) -> Dict[str, Any]:
        """从DOCX文件提取文本"""
        try:
            from docx import Document
            
            def _read_docx():
                doc = Document(file_path)
                content = ""
                
                for paragraph in doc.paragraphs:
                    if paragraph.text.strip():
                        content += paragraph.text + "\n"
                
                return content
            
            # 在线程池中执行
            content = await asyncio.get_event_loop().run_in_executor(
                None, _read_docx
            )
            
            content = self._clean_text(content)
            
            return {
                "title": file_path.stem,
                "content": content,
                "chapters": [{"title": "全文", "content": content}],
                "word_count": len(content),
                "metadata": {
                    "file_type": "docx"
                }
            }
            
        except ImportError:
            logger.error("缺少python-docx依赖，无法处理DOCX文件")
            raise ValueError("系统不支持DOCX文件格式")
        except Exception as e:
            logger.error(f"DOCX文件提取失败: {str(e)}")
            raise
    
    def _clean_text(self, text: str) -> str:
        """清理文本内容"""
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除特殊字符但保留基本标点
        text = re.sub(r'[^\w\s\u4e00-\u9fff.,!?;:()""''—\-]', '', text)
        
        # 规范化换行
        text = re.sub(r'\n\s*\n', '\n\n', text)
        
        return text.strip()


# 创建全局文本提取服务实例
text_extraction_service = TextExtractionService()
