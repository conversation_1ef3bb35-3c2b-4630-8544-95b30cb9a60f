"""
任务处理服务
负责协调文件上传后的任务处理流程
"""

import asyncio
from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from pathlib import Path

from app.core.logging import get_logger
from app.core.database import get_db
from app.models.task import Task, TaskStatus
from app.services.audio_conversion_service import audio_conversion_service
from app.services.storage_service import storage_service

logger = get_logger(__name__)


class TaskProcessingService:
    """任务处理服务"""
    
    def __init__(self):
        self.processing_tasks = set()  # 正在处理的任务ID
    
    async def start_task_processing(self, task_id: int) -> None:
        """
        启动任务处理
        
        Args:
            task_id: 任务ID
        """
        if task_id in self.processing_tasks:
            logger.warning(f"任务 {task_id} 已在处理中")
            return
        
        self.processing_tasks.add(task_id)
        
        try:
            # 在后台异步处理任务
            asyncio.create_task(self._process_task(task_id))
            logger.info(f"任务 {task_id} 已加入处理队列")
            
        except Exception as e:
            logger.error(f"启动任务处理失败: {str(e)}")
            self.processing_tasks.discard(task_id)
            raise
    
    async def _process_task(self, task_id: int) -> None:
        """
        处理单个任务
        
        Args:
            task_id: 任务ID
        """
        db = None
        try:
            # 获取数据库会话
            db = next(get_db())
            
            # 获取任务信息
            task = db.query(Task).filter(Task.id == task_id).first()
            if not task:
                logger.error(f"任务不存在: {task_id}")
                return
            
            logger.info(f"开始处理任务: {task_id}, 用户: {task.user_id}")
            
            # 更新任务状态为处理中
            task.status = TaskStatus.PROCESSING
            task.progress = 0
            db.commit()
            
            # 获取文件路径
            if not task.file_url:
                raise ValueError("任务没有关联的文件")
            
            # 从文件URL获取本地路径
            local_path = storage_service.get_local_file_path_from_url(task.file_url)
            if not local_path or not local_path.exists():
                raise ValueError(f"文件不存在: {task.file_url}")
            
            # 开始音频转换
            conversion_result = None
            async for update in audio_conversion_service.convert_file_to_audio(
                task_id=task.id,
                user_id=task.user_id,
                file_path=str(local_path),
                file_type=task.task_type.value,
                voice_settings=task.voice_settings
            ):
                # 更新任务进度
                if update["type"] == "progress":
                    task.progress = update["progress"]
                    db.commit()
                    logger.info(f"任务 {task_id} 进度: {update['progress']}% - {update['message']}")
                
                elif update["type"] == "complete":
                    conversion_result = update["result"]
                    task.progress = 100
                    break
                
                elif update["type"] == "error":
                    raise ValueError(update["message"])
            
            if not conversion_result:
                raise ValueError("音频转换未返回结果")
            
            # 更新任务结果
            task.status = TaskStatus.COMPLETED
            task.progress = 100
            task.audio_files = conversion_result["audio_files"]
            task.playlist_url = conversion_result["playlist_url"]
            task.total_duration = conversion_result["total_duration"]
            task.error_message = None
            
            db.commit()
            
            logger.info(f"任务处理完成: {task_id}")
            
        except Exception as e:
            logger.error(f"任务处理失败: {task_id}, 错误: {str(e)}")
            
            # 更新任务状态为失败
            if db:
                try:
                    task = db.query(Task).filter(Task.id == task_id).first()
                    if task:
                        task.status = TaskStatus.FAILED
                        task.error_message = str(e)
                        db.commit()
                except Exception as db_error:
                    logger.error(f"更新任务失败状态时出错: {str(db_error)}")
        
        finally:
            # 清理
            if db:
                db.close()
            self.processing_tasks.discard(task_id)
    
    def get_processing_tasks(self) -> set:
        """获取正在处理的任务ID列表"""
        return self.processing_tasks.copy()
    
    def is_task_processing(self, task_id: int) -> bool:
        """检查任务是否正在处理"""
        return task_id in self.processing_tasks


# 创建全局任务处理服务实例
task_processing_service = TaskProcessingService()
