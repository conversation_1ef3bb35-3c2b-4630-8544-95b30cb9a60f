#!/usr/bin/env python3
"""
测试修复效果的脚本
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'audiobooks_monolith'))

from app.core.database import get_db
from app.models.task import Task, TaskStatus
from app.api.audio import get_audio_library
from app.models.user import User
from sqlalchemy.orm import Session

def test_task_data_integrity():
    """测试任务数据完整性"""
    print("=== 测试任务数据完整性 ===")
    
    db = next(get_db())
    
    # 查询已完成的任务
    completed_tasks = db.query(Task).filter(Task.status == TaskStatus.COMPLETED).all()
    
    print(f"已完成任务数量: {len(completed_tasks)}")
    
    for task in completed_tasks:
        print(f"\n任务ID: {task.id}")
        print(f"标题: {task.title}")
        print(f"原始文件名: {task.original_filename}")
        print(f"文件大小: {task.file_size}")
        print(f"总时长: {task.total_duration}")
        print(f"音频文件数量: {len(task.audio_files) if task.audio_files else 0}")
        print(f"播放列表URL: {task.playlist_url}")
        print(f"任务类型: {task.task_type}")
        
        # 检查关键字段是否存在
        issues = []
        if not task.audio_files:
            issues.append("缺少audio_files")
        if not task.playlist_url:
            issues.append("缺少playlist_url")
        if not task.total_duration:
            issues.append("缺少total_duration")
        if not task.original_filename:
            issues.append("缺少original_filename")
        if not task.file_size:
            issues.append("缺少file_size")
            
        if issues:
            print(f"⚠️  数据问题: {', '.join(issues)}")
        else:
            print("✅ 数据完整")
    
    db.close()

def test_audio_library_api():
    """测试音频库API（模拟调用）"""
    print("\n=== 测试音频库API ===")
    
    try:
        # 模拟API调用的核心逻辑
        db = next(get_db())
        
        # 查询用户已完成的任务（模拟用户ID为4）
        tasks = db.query(Task).filter(
            Task.user_id == 4,
            Task.status == TaskStatus.COMPLETED
        ).all()
        
        print(f"找到已完成任务: {len(tasks)}")
        
        # 转换为音频库格式（复制API逻辑）
        audio_books = []
        for task in tasks:
            # 检查是否有音频文件或播放列表
            audio_url = None
            playlist_url = None
            
            if task.audio_files or task.playlist_url:
                audio_url = f"/api/audio/{task.id}"
                playlist_url = task.playlist_url
            
            # 格式化时长信息
            duration_formatted = None
            if task.total_duration:
                hours = task.total_duration // 3600
                minutes = (task.total_duration % 3600) // 60
                seconds = task.total_duration % 60
                if hours > 0:
                    duration_formatted = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                else:
                    duration_formatted = f"{minutes:02d}:{seconds:02d}"
            
            # 格式化文件大小
            size_formatted = None
            if task.file_size:
                if task.file_size < 1024:
                    size_formatted = f"{task.file_size} B"
                elif task.file_size < 1024 * 1024:
                    size_formatted = f"{task.file_size / 1024:.1f} KB"
                else:
                    size_formatted = f"{task.file_size / (1024 * 1024):.1f} MB"
            
            audio_book = {
                "id": task.id,
                "title": task.title or "未命名",
                "filename": task.original_filename or "",
                "status": task.status.value,
                "audioUrl": audio_url,
                "playlistUrl": playlist_url,
                "totalChapters": len(task.audio_files) if task.audio_files else 1,
                "isEnhanced": task.processing_mode == "enhanced",
                "createdAt": task.created_at.isoformat(),
                "updatedAt": task.updated_at.isoformat(),
                "duration": duration_formatted,
                "durationSeconds": task.total_duration,
                "size": size_formatted,
                "fileSize": task.file_size,
                "taskType": task.task_type.value
            }
            audio_books.append(audio_book)
            
            print(f"\n音频书籍: {audio_book['title']}")
            print(f"  文件名: {audio_book['filename']}")
            print(f"  时长: {audio_book['duration']}")
            print(f"  大小: {audio_book['size']}")
            print(f"  章节数: {audio_book['totalChapters']}")
            print(f"  音频URL: {audio_book['audioUrl']}")
            print(f"  播放列表: {audio_book['playlistUrl']}")
        
        print(f"\n✅ 音频库API测试成功，返回 {len(audio_books)} 个音频书籍")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 音频库API测试失败: {str(e)}")
        return False

def test_frontend_data_formatting():
    """测试前端数据格式化逻辑"""
    print("\n=== 测试前端数据格式化 ===")
    
    # 模拟后端返回的原始任务数据
    raw_task = {
        "id": 1,
        "title": "测试任务",
        "task_type": "txt",
        "status": "completed",
        "original_filename": "test.txt",
        "file_size": 1024,
        "total_duration": 125,  # 2分5秒
        "created_at": "2025-06-21T19:25:47.737315",
        "updated_at": "2025-06-21T19:26:47.737315"
    }
    
    # 模拟前端格式化逻辑
    type_display_map = {
        'txt': 'TXT文档',
        'epub': 'EPUB电子书',
        'pdf': 'PDF文档',
        'docx': 'Word文档',
        'url': '网页链接'
    }
    
    def format_file_size(bytes_val):
        if not bytes_val:
            return ''
        if bytes_val < 1024:
            return f"{bytes_val} B"
        if bytes_val < 1024 * 1024:
            return f"{bytes_val / 1024:.1f} KB"
        return f"{bytes_val / (1024 * 1024):.1f} MB"
    
    def format_duration(seconds):
        if not seconds:
            return ''
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        secs = seconds % 60
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{secs:02d}"
        return f"{minutes:02d}:{secs:02d}"
    
    # 格式化数据
    formatted_task = {
        **raw_task,
        "typeDisplay": type_display_map.get(raw_task["task_type"], raw_task["task_type"]),
        "audioSizeFormatted": format_file_size(raw_task["file_size"]),
        "durationFormatted": format_duration(raw_task["total_duration"]),
        "filename": raw_task["original_filename"] or raw_task["title"]
    }
    
    print("原始数据:")
    print(f"  task_type: {raw_task['task_type']}")
    print(f"  file_size: {raw_task['file_size']}")
    print(f"  total_duration: {raw_task['total_duration']}")
    
    print("\n格式化后:")
    print(f"  typeDisplay: {formatted_task['typeDisplay']}")
    print(f"  audioSizeFormatted: {formatted_task['audioSizeFormatted']}")
    print(f"  durationFormatted: {formatted_task['durationFormatted']}")
    print(f"  filename: {formatted_task['filename']}")
    
    # 检查是否有undefined值
    undefined_fields = []
    for key, value in formatted_task.items():
        if value is None or value == '':
            undefined_fields.append(key)
    
    if undefined_fields:
        print(f"⚠️  可能显示为undefined的字段: {', '.join(undefined_fields)}")
    else:
        print("✅ 所有字段都有有效值")

if __name__ == "__main__":
    print("开始测试修复效果...\n")
    
    test_task_data_integrity()
    api_success = test_audio_library_api()
    test_frontend_data_formatting()
    
    print("\n=== 测试总结 ===")
    if api_success:
        print("✅ 所有测试通过，修复成功！")
    else:
        print("❌ 部分测试失败，需要进一步检查")
